/**
 * 自然语言场景生成器
 * 基于自然语言描述生成3D场景
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Scene } from '../scene/Scene';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Transform } from '../scene/Transform';
import { Light, LightType } from '../rendering/Light';
import { MeshComponent } from '../rendering/MeshComponent';
import { MaterialFactory, MaterialType } from '../rendering/materials/MaterialFactory';

export interface GenerationOptions {
  style: 'realistic' | 'cartoon' | 'minimalist' | 'scifi' | 'fantasy';
  quality: number; // 1-100
  maxObjects: number;
  constraints: {
    maxPolygons: number;
    targetFrameRate: number;
  };
  onProgress?: (progress: number) => void;
}

export interface LanguageUnderstanding {
  entities: Array<{
    text: string;
    type: 'OBJECT' | 'LOCATION' | 'ATTRIBUTE' | 'ACTION';
    confidence: number;
  }>;
  intent: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  style: string;
}

export class NLPSceneGenerator extends System {
  static readonly NAME = 'NLPSceneGenerator';

  private eventEmitter: EventEmitter;
  private cache: Map<string, Scene>;
  private isInitialized: boolean = false;

  constructor() {
    super(350); // 系统优先级
    this.eventEmitter = new EventEmitter();
    this.cache = new Map();
  }

  public initialize(): void {
    if (this.isInitialized) return;

    console.log('初始化自然语言场景生成器...');
    this.isInitialized = true;
  }

  /**
   * 从自然语言生成场景
   */
  public async generateSceneFromNaturalLanguage(
    userInput: string,
    options: GenerationOptions = {
      style: 'realistic',
      quality: 80,
      maxObjects: 50,
      constraints: {
        maxPolygons: 100000,
        targetFrameRate: 60
      }
    }
  ): Promise<Scene> {
    if (!this.isInitialized) {
      throw new Error('NLPSceneGenerator 未初始化');
    }

    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey(userInput, options);

      // 检查缓存
      if (this.cache.has(cacheKey)) {
        console.log('从缓存返回场景');
        return this.cache.get(cacheKey)!;
      }

      // 第一步：自然语言理解 (20%)
      options.onProgress?.(20);
      const understanding = await this.understandText(userInput);

      // 第二步：场景规划 (40%)
      options.onProgress?.(40);
      const scenePlan = await this.planScene(understanding, options);

      // 第三步：生成3D内容 (80%)
      options.onProgress?.(80);
      const scene = await this.generateScene(scenePlan, options);

      // 第四步：后处理优化 (100%)
      options.onProgress?.(100);
      await this.optimizeScene(scene, understanding);

      // 缓存结果
      this.cache.set(cacheKey, scene);

      // 触发事件
      this.eventEmitter.emit('sceneGenerated', {
        userInput,
        scene,
        understanding,
        options
      });

      return scene;

    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.eventEmitter.emit('generationError', { userInput, error });
      throw error;
    }
  }

  /**
   * 理解自然语言文本
   */
  public async understandText(text: string): Promise<LanguageUnderstanding> {
    // 简化的文本理解实现
    const entities = this.extractEntities(text);
    const intent = this.classifyIntent(text);
    const sentiment = this.analyzeSentiment(text);
    const keywords = this.extractKeywords(text);
    const style = this.inferStyle(text);

    return {
      entities,
      intent,
      sentiment,
      keywords,
      style
    };
  }

  /**
   * 提取实体
   */
  private extractEntities(text: string): LanguageUnderstanding['entities'] {
    const entities: LanguageUnderstanding['entities'] = [];

    // 对象实体
    const objectPatterns = [
      /桌子|椅子|沙发|床|柜子|书架/g,
      /电脑|电视|灯|植物|花|树/g,
      /汽车|建筑|房子|门|窗户/g
    ];

    objectPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'OBJECT',
            confidence: 0.8
          });
        });
      }
    });

    // 位置实体
    const locationPatterns = [
      /办公室|客厅|卧室|厨房|浴室/g,
      /学校|图书馆|咖啡厅|餐厅|商店/g,
      /公园|广场|街道|花园/g
    ];

    locationPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'LOCATION',
            confidence: 0.9
          });
        });
      }
    });

    // 属性实体
    const attributePatterns = [
      /现代|古典|简约|豪华|温馨/g,
      /明亮|昏暗|宽敞|狭小|舒适/g,
      /红色|蓝色|绿色|白色|黑色/g
    ];

    attributePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'ATTRIBUTE',
            confidence: 0.7
          });
        });
      }
    });

    return entities;
  }

  /**
   * 分类意图
   */
  private classifyIntent(text: string): string {
    if (/创建|建造|制作|生成/.test(text)) return 'CREATE';
    if (/修改|改变|调整|更新/.test(text)) return 'MODIFY';
    if (/删除|移除|清除/.test(text)) return 'DELETE';
    return 'CREATE'; // 默认为创建意图
  }

  /**
   * 分析情感
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['美丽', '温馨', '舒适', '明亮', '愉快', '漂亮'];
    const negativeWords = ['阴暗', '破旧', '肮脏', '混乱', '压抑'];

    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简化的关键词提取
    const words = text.split(/\s+|，|。|、/);
    return words.filter(word => word.length > 1 && !/的|了|在|和|与|或/.test(word));
  }

  /**
   * 推断风格
   */
  private inferStyle(text: string): string {
    if (/科幻|未来|太空|机器人/.test(text)) return 'scifi';
    if (/卡通|可爱|童话|动画/.test(text)) return 'cartoon';
    if (/简约|极简|现代|简洁/.test(text)) return 'minimalist';
    if (/魔法|奇幻|龙|城堡/.test(text)) return 'fantasy';
    return 'realistic';
  }

  /**
   * 规划场景
   */
  private async planScene(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): Promise<any> {
    const plan = {
      layout: this.planLayout(understanding),
      objects: this.planObjects(understanding, options),
      lighting: this.planLighting(understanding),
      materials: this.planMaterials(understanding),
      atmosphere: this.planAtmosphere(understanding)
    };

    return plan;
  }

  /**
   * 规划布局
   */
  private planLayout(understanding: LanguageUnderstanding): any {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      // 根据位置类型返回不同的布局
      switch (location) {
        case '办公室':
          return {
            type: 'office',
            size: { width: 10, height: 3, depth: 8 },
            zones: ['work_area', 'meeting_area', 'storage_area']
          };
        case '客厅':
          return {
            type: 'living_room',
            size: { width: 12, height: 3, depth: 10 },
            zones: ['seating_area', 'entertainment_area', 'decoration_area']
          };
        case '图书馆':
          return {
            type: 'library',
            size: { width: 20, height: 4, depth: 15 },
            zones: ['reading_area', 'book_storage', 'study_area']
          };
        default:
          return {
            type: 'generic',
            size: { width: 10, height: 3, depth: 10 },
            zones: ['main_area']
          };
      }
    }

    return {
      type: 'generic',
      size: { width: 10, height: 3, depth: 10 },
      zones: ['main_area']
    };
  }

  /**
   * 规划对象
   */
  private planObjects(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): any[] {
    const objects = understanding.entities.filter(e => e.type === 'OBJECT');
    const plannedObjects = [];

    // 限制对象数量
    const maxObjects = Math.min(objects.length, options.maxObjects || 50);

    for (let i = 0; i < maxObjects; i++) {
      const obj = objects[i % objects.length];
      plannedObjects.push({
        type: obj.text,
        position: this.generateRandomPosition(),
        rotation: this.generateRandomRotation(),
        scale: this.generateRandomScale(),
        material: this.selectMaterial(obj.text)
      });
    }

    return plannedObjects;
  }

  /**
   * 生成随机位置
   */
  private generateRandomPosition(): { x: number, y: number, z: number } {
    return {
      x: (Math.random() - 0.5) * 10,
      y: 0,
      z: (Math.random() - 0.5) * 10
    };
  }

  /**
   * 生成随机旋转
   */
  private generateRandomRotation(): { x: number, y: number, z: number } {
    return {
      x: 0,
      y: Math.random() * Math.PI * 2,
      z: 0
    };
  }

  /**
   * 生成随机缩放
   */
  private generateRandomScale(): { x: number, y: number, z: number } {
    const scale = 0.8 + Math.random() * 0.4; // 0.8 到 1.2
    return { x: scale, y: scale, z: scale };
  }

  /**
   * 选择材质
   */
  private selectMaterial(objectType: string): string {
    const materialMap: { [key: string]: string } = {
      '桌子': 'wood',
      '椅子': 'fabric',
      '沙发': 'leather',
      '电脑': 'plastic',
      '植物': 'organic',
      '灯': 'metal'
    };

    return materialMap[objectType] || 'default';
  }

  /**
   * 规划光照
   */
  private planLighting(understanding: LanguageUnderstanding): any {
    const sentiment = understanding.sentiment;
    const attributes = understanding.entities.filter(e => e.type === 'ATTRIBUTE');

    let intensity = 1.0;
    let color = '#ffffff';
    let warmth = 0.5;

    // 根据情感调整光照
    if (sentiment === 'positive') {
      intensity = 1.2;
      warmth = 0.7;
    } else if (sentiment === 'negative') {
      intensity = 0.6;
      warmth = 0.3;
    }

    // 根据属性调整光照
    attributes.forEach(attr => {
      switch (attr.text) {
        case '明亮':
          intensity = 1.5;
          break;
        case '昏暗':
          intensity = 0.4;
          break;
        case '温馨':
          warmth = 0.8;
          color = '#fff8dc';
          break;
      }
    });

    return {
      ambient: {
        intensity: intensity * 0.3,
        color
      },
      directional: {
        intensity: intensity * 0.7,
        color,
        direction: { x: -1, y: -1, z: -1 }
      },
      warmth
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userInput: string, options: GenerationOptions): string {
    return `${userInput}_${options.style}_${options.quality}_${options.maxObjects}`;
  }

  /**
   * 规划材质
   */
  private planMaterials(understanding: LanguageUnderstanding): any[] {
    const style = understanding.style;
    const materials = [];

    switch (style) {
      case 'realistic':
        materials.push(
          { name: 'wood', type: 'physical', roughness: 0.8, metalness: 0.0 },
          { name: 'metal', type: 'physical', roughness: 0.2, metalness: 1.0 },
          { name: 'fabric', type: 'physical', roughness: 0.9, metalness: 0.0 }
        );
        break;
      case 'cartoon':
        materials.push(
          { name: 'toon', type: 'toon', color: '#ff6b6b' },
          { name: 'bright', type: 'basic', color: '#4ecdc4' }
        );
        break;
      default:
        materials.push(
          { name: 'default', type: 'standard', color: '#cccccc' }
        );
    }

    return materials;
  }

  /**
   * 规划氛围
   */
  private planAtmosphere(understanding: LanguageUnderstanding): any {
    return {
      fog: understanding.sentiment === 'negative' ? 0.1 : 0.0,
      skybox: understanding.style === 'scifi' ? 'space' : 'default',
      ambientSound: this.selectAmbientSound(understanding)
    };
  }

  /**
   * 选择环境音效
   */
  private selectAmbientSound(understanding: LanguageUnderstanding): string {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      switch (location) {
        case '办公室': return 'office_ambient';
        case '咖啡厅': return 'cafe_ambient';
        case '图书馆': return 'library_ambient';
        case '公园': return 'nature_ambient';
        default: return 'default_ambient';
      }
    }

    return 'default_ambient';
  }

  /**
   * 生成场景
   */
  private async generateScene(plan: any, options: GenerationOptions): Promise<Scene> {
    const scene = new Scene();
    scene.name = `Generated Scene ${Date.now()}`;

    // 创建地面
    this.createGround(scene, plan.layout);

    // 创建对象
    plan.objects.forEach((objPlan: any) => {
      this.createObject(scene, objPlan);
    });

    // 设置光照
    this.setupLighting(scene, plan.lighting);

    // 应用材质
    this.applyMaterials(scene, plan.materials);

    // 设置氛围
    this.setupAtmosphere(scene, plan.atmosphere);

    return scene;
  }

  /**
   * 创建地面
   */
  private createGround(scene: Scene, layout: any): void {
    const groundEntity = new Entity('Ground');

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(0, -0.1, 0);
    transform.setScale(layout.size.width, 0.2, layout.size.depth);
    groundEntity.addComponent(transform);

    // 创建地面几何体
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);

    // 创建地面材质
    const materialFactory = new MaterialFactory();
    const groundMaterial = materialFactory.createStandardMaterial({
      color: 0x808080,
      roughness: 0.8,
      metalness: 0.2
    });

    // 创建网格组件
    const meshComponent = new MeshComponent({
      geometry: groundGeometry,
      material: groundMaterial
    });
    groundEntity.addComponent(meshComponent);

    // 添加到场景
    scene.addEntity(groundEntity);
  }

  /**
   * 创建对象
   */
  private createObject(scene: Scene, objPlan: any): void {
    const entity = new Entity(objPlan.type);

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(objPlan.position.x, objPlan.position.y, objPlan.position.z);
    transform.setRotation(objPlan.rotation.x, objPlan.rotation.y, objPlan.rotation.z);
    transform.setScale(objPlan.scale.x, objPlan.scale.y, objPlan.scale.z);
    entity.addComponent(transform);

    // 根据对象类型创建几何体
    let geometry: THREE.BufferGeometry;
    switch (objPlan.type) {
      case '桌子':
        geometry = new THREE.BoxGeometry(2, 0.1, 1);
        break;
      case '椅子':
        geometry = new THREE.BoxGeometry(0.5, 1, 0.5);
        break;
      case '沙发':
        geometry = new THREE.BoxGeometry(2, 0.8, 1);
        break;
      case '电脑':
        geometry = new THREE.BoxGeometry(0.4, 0.3, 0.05);
        break;
      case '植物':
        geometry = new THREE.ConeGeometry(0.3, 1, 8);
        break;
      case '灯':
        geometry = new THREE.SphereGeometry(0.2, 16, 16);
        break;
      case '汽车':
        geometry = new THREE.BoxGeometry(4, 1.5, 2);
        break;
      case '建筑':
      case '房子':
        geometry = new THREE.BoxGeometry(5, 3, 5);
        break;
      default:
        geometry = new THREE.BoxGeometry(1, 1, 1);
    }

    // 创建材质
    const materialFactory = new MaterialFactory();
    let material: THREE.Material;

    switch (objPlan.material) {
      case 'wood':
        material = materialFactory.createStandardMaterial({
          color: 0x8B4513,
          roughness: 0.8,
          metalness: 0.1
        });
        break;
      case 'metal':
        material = materialFactory.createPhysicalMaterial({
          color: 0xC0C0C0,
          roughness: 0.2,
          metalness: 1.0
        });
        break;
      case 'fabric':
        material = materialFactory.createStandardMaterial({
          color: 0x4169E1,
          roughness: 0.9,
          metalness: 0.0
        });
        break;
      case 'plastic':
        material = materialFactory.createStandardMaterial({
          color: 0x333333,
          roughness: 0.3,
          metalness: 0.0
        });
        break;
      case 'organic':
        material = materialFactory.createStandardMaterial({
          color: 0x228B22,
          roughness: 0.9,
          metalness: 0.0
        });
        break;
      case 'leather':
        material = materialFactory.createStandardMaterial({
          color: 0x8B4513,
          roughness: 0.7,
          metalness: 0.0
        });
        break;
      default:
        material = materialFactory.createStandardMaterial({
          color: 0xCCCCCC,
          roughness: 0.5,
          metalness: 0.2
        });
    }

    // 创建网格组件
    const meshComponent = new MeshComponent({
      geometry: geometry,
      material: material
    });
    entity.addComponent(meshComponent);

    // 添加到场景
    scene.addEntity(entity);
  }

  /**
   * 设置光照
   */
  private setupLighting(scene: Scene, lighting: any): void {
    // 创建环境光
    const ambientEntity = new Entity('AmbientLight');
    const ambientTransform = new Transform();
    ambientEntity.addComponent(ambientTransform);

    const ambientLight = new Light({
      type: LightType.AMBIENT,
      color: lighting.ambient.color,
      intensity: lighting.ambient.intensity
    });
    ambientEntity.addComponent(ambientLight);
    scene.addEntity(ambientEntity);

    // 创建方向光
    const directionalEntity = new Entity('DirectionalLight');
    const directionalTransform = new Transform();
    directionalTransform.setPosition(
      lighting.directional.direction.x * 10,
      lighting.directional.direction.y * 10,
      lighting.directional.direction.z * 10
    );
    directionalEntity.addComponent(directionalTransform);

    const directionalLight = new Light({
      type: LightType.DIRECTIONAL,
      color: lighting.directional.color,
      intensity: lighting.directional.intensity,
      castShadow: true
    });
    directionalEntity.addComponent(directionalLight);
    scene.addEntity(directionalEntity);

    // 如果需要，添加点光源增强氛围
    if (lighting.warmth > 0.6) {
      const pointEntity = new Entity('WarmPointLight');
      const pointTransform = new Transform();
      pointTransform.setPosition(0, 3, 0);
      pointEntity.addComponent(pointTransform);

      const pointLight = new Light({
        type: LightType.POINT,
        color: '#fff8dc',
        intensity: lighting.warmth * 0.5,
        distance: 10,
        decay: 2
      } as any); // 临时使用any类型，因为接口定义可能不完整
      pointEntity.addComponent(pointLight);
      scene.addEntity(pointEntity);
    }
  }

  /**
   * 应用材质
   */
  private applyMaterials(scene: Scene, materials: any[]): void {
    // 获取场景中的所有实体
    const entities = scene.getEntities();
    const materialFactory = new MaterialFactory();

    // 为每个实体应用合适的材质
    entities.forEach(entity => {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent && entity.name !== 'Ground') {
        // 根据材质列表选择合适的材质
        const materialData = materials.find(m => m.name === 'default') || materials[0];

        if (materialData) {
          let material: THREE.Material;

          switch (materialData.type) {
            case 'physical':
              material = materialFactory.createPhysicalMaterial({
                color: materialData.color || 0xcccccc,
                roughness: materialData.roughness || 0.5,
                metalness: materialData.metalness || 0.0
              });
              break;
            case 'toon':
              material = materialFactory.createToonMaterial({
                color: materialData.color || 0xff6b6b
              });
              break;
            case 'basic':
              material = materialFactory.createBasicMaterial({
                color: materialData.color || 0x4ecdc4
              });
              break;
            default:
              material = materialFactory.createStandardMaterial({
                color: materialData.color || 0xcccccc
              });
          }

          // 更新网格组件的材质
          meshComponent.setMaterial(material);
        }
      }
    });
  }

  /**
   * 设置氛围
   */
  private setupAtmosphere(scene: Scene, atmosphere: any): void {
    // 设置雾效
    if (atmosphere.fog > 0) {
      const threeScene = scene.getThreeScene();
      if (threeScene) {
        const fogNear = 5;
        const fogFar = 50 * (1 + atmosphere.fog);
        threeScene.fog = new THREE.Fog(0x888888, fogNear, fogFar);
      }
    }

    // 设置天空盒
    if (atmosphere.skybox && atmosphere.skybox !== 'default') {
      const threeScene = scene.getThreeScene();
      if (threeScene) {
        let skyboxColor: THREE.Color;
        switch (atmosphere.skybox) {
          case 'space':
            skyboxColor = new THREE.Color(0x000011);
            break;
          case 'sunset':
            skyboxColor = new THREE.Color(0xff6b35);
            break;
          case 'dawn':
            skyboxColor = new THREE.Color(0x87ceeb);
            break;
          default:
            skyboxColor = new THREE.Color(0x87ceeb);
        }
        threeScene.background = skyboxColor;
      }
    }

    // 设置环境音效（这里只是记录，实际音效需要音频系统处理）
    if (atmosphere.ambientSound) {
      console.log(`设置环境音效: ${atmosphere.ambientSound}`);
      // 实际实现需要音频系统支持
      // audioSystem.playAmbientSound(atmosphere.ambientSound);
    }
  }

  /**
   * 优化场景
   */
  private async optimizeScene(scene: Scene, understanding: LanguageUnderstanding): Promise<void> {
    // 根据情感调整场景
    if (understanding.sentiment === 'positive') {
      this.enhanceBrightness(scene);
    } else if (understanding.sentiment === 'negative') {
      this.reduceBrightness(scene);
    }

    // 性能优化
    await this.optimizePerformance(scene);
  }

  /**
   * 增强亮度
   */
  private enhanceBrightness(scene: Scene): void {
    // 获取场景中的所有光源实体
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const lightComponent = entity.getComponent('Light') as Light;
      if (lightComponent) {
        // 增加光源强度
        const currentIntensity = lightComponent.getThreeLight().intensity;
        lightComponent.setIntensity(currentIntensity * 1.2);
      }
    });

    // 调整环境光
    const threeScene = scene.getThreeScene();
    if (threeScene && threeScene.background instanceof THREE.Color) {
      threeScene.background.multiplyScalar(1.1);
    }
  }

  /**
   * 降低亮度
   */
  private reduceBrightness(scene: Scene): void {
    // 获取场景中的所有光源实体
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const lightComponent = entity.getComponent('Light') as Light;
      if (lightComponent) {
        // 降低光源强度
        const currentIntensity = lightComponent.getThreeLight().intensity;
        lightComponent.setIntensity(currentIntensity * 0.8);
      }
    });

    // 调整环境光
    const threeScene = scene.getThreeScene();
    if (threeScene && threeScene.background instanceof THREE.Color) {
      threeScene.background.multiplyScalar(0.9);
    }
  }

  /**
   * 性能优化
   */
  private async optimizePerformance(scene: Scene): Promise<void> {
    const entities = scene.getEntities();
    let totalPolygons = 0;

    // 统计多边形数量
    entities.forEach(entity => {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent) {
        const geometry = meshComponent.geometry;
        if (geometry && geometry.attributes.position) {
          totalPolygons += geometry.attributes.position.count / 3;
        }
      }
    });

    console.log(`场景总多边形数: ${totalPolygons}`);

    // 如果多边形数量过多，进行优化
    if (totalPolygons > 100000) {
      console.log('场景复杂度过高，进行性能优化...');

      // 简化远距离对象的几何体
      entities.forEach(entity => {
        const transform = entity.getTransform();
        const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;

        if (transform && meshComponent) {
          const position = transform.getPosition();
          const distance = position.length();

          // 距离原点较远的对象使用简化几何体
          if (distance > 10) {
            const geometry = meshComponent.geometry;
            if (geometry instanceof THREE.BoxGeometry) {
              // 减少分段数
              const simplifiedGeometry = new THREE.BoxGeometry(
                geometry.parameters.width,
                geometry.parameters.height,
                geometry.parameters.depth,
                1, 1, 1 // 最少分段
              );
              meshComponent.setGeometry(simplifiedGeometry);
            }
          }
        }
      });
    }
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
